package com.wbgame.controller.waibao;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.wbgame.common.Constants;
import com.wbgame.common.ReturnJson;
import com.wbgame.controller.SysTempController;
import com.wbgame.controller.WbSysController;
import com.wbgame.mapper.master.waibao.MobileGameWaiBaoSysMapper;
import com.wbgame.pojo.CurrUserVo;
import com.wbgame.pojo.SysTempVo;
import com.wbgame.pojo.WaibaoUserVo;
import com.wbgame.utils.BlankUtils;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/* ```
应用列表_查询 /mobile/selectApp ID_200521101105 http://localhost:6105/mobile/selectApp?role_id&role_name
应用列表_操作 /mobile/conApp ID_200521183023 http://localhost:6105/mobile/conApp?handle&app_key&app_name

角色列表_查询 /mobile/selectRole ID_200519193239 http://localhost:6105/mobile/selectRole?role_id&role_name
角色列表_操作 /mobile/conRole ID_200521101019 http://localhost:6105/mobile/conRole?handle&role_id&role_name&role_app_slot
菜单设置_查询 /mobile/menu ID_200423122058 http://localhost:6105/mobile/menu?style=page&title&index
菜单设置_操作 /mobile/menuSet ID_200423191535 http://localhost:6105/mobile/menuSet?handle=add.edit.del&index&title&style&menu&off&icon&slot
权限接口_查询 /mobile/org ID_200424161225 http://localhost:6105/mobile/org?org_id&org_name&hidden_menu_list&page_list
权限接口_操作 /mobile/orgSet ID_200424164102 http://localhost:6105/mobile/orgSet?org_id&org_name&hidden_menu_list&page_list
用户管理_查询 /mobile/userList ID_200427142009 http://localhost:6105/mobile/userList?user_id&password&user_name&org_id&sys
用户管理_操作 /mobile/userListSet ID_200427144719 http://localhost:6105/mobile/userListSet?user_id&password&user_name&org_id&sys
独立用户列表_查询 /mobile/userCk ID_200423103403 http://localhost:6105/mobile/userCk?user_id=adad&user_name
*/

/**
 * 移动游戏独立系统
 *
 * <AUTHOR>
 * @date 2020/10/23
 */
@CrossOrigin
@Controller
public class MobileGameWaiBaoSysController {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    @Autowired
    private MobileGameWaiBaoSysMapper wbMapper;

    @Autowired
    private SysTempController sysTempController;

    @Autowired
    private WbSysController wbSysController;

    /**
     * 从request中获取请求方IP
     * @param request
     * @return
     */
    public static String getIpAddress(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

    /* 登陆多验证接口 /mobile/logInIf ID_201119122103 */
    @RequestMapping(value = "/mobile/logInIf", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public String logInIf(HttpServletRequest request, HttpServletResponse response) {
        try {
            /* 处理加密的b64参数 */
            String v = request.getParameter("value");
            if (BlankUtils.checkBlank(v)) {
                System.out.println("ip:"+getIpAddress(request));
                return ReturnJson.error(Constants.ParamError);
            }
            String replace = request.getParameter("value").replace(" ", "+");
            String value = new String(Base64.decodeBase64(replace), "UTF-8");
            JSONObject obj = JSONObject.parseObject(value);
            /* 用户验证 */
            int userBack = -1;
            String org_id_in = null;
            if (obj.getString("user_id") != null) {
                Map<String, Object> selectMap = new HashMap<>();
                selectMap.put("user_id", obj.getString("user_id"));
                SysTempVo userObj = wbMapper.userIf(selectMap);
                userBack = userObj == null ? 0 : 1;
                if (userBack == 1) {
                    org_id_in = userObj.getOrg_id();
                }
            }
            /* 权限验证 */
            int orgBack = -1;
            String org_sys_type = null;
            if (obj.getString("org_id") != null || org_id_in != null) {
                String nowOrgId = obj.getString("org_id") != null ? obj.getString("org_id") : org_id_in;
                Map<String, Object> orgSQLVal = new HashMap<>();
                orgSQLVal.put("org_id", nowOrgId);
                SysTempVo orgObj = wbMapper.orgIf(orgSQLVal);
                orgBack = orgObj == null ? 0 : 1;
                if (orgObj != null) {
                    org_sys_type = orgObj.getSys_type();
                }
            }
            /* 格式化输出 */
            JSONObject out = new JSONObject();
            out.put("ret", 1);
            out.put("msg", "success");
            out.put("user", userBack);//验证账户返回结果
            out.put("org", orgBack);//验证权限返回结果
            out.put("org_sys_type", org_sys_type);//验证权限返回结果
            return out.toJSONString();
        } catch (Exception e) {
            e.printStackTrace();
            JSONObject result = new JSONObject();
            result.put("ret", 0);
            result.put("msg", "错误信息 : " + e.getMessage());
            return result.toJSONString();
        }
    }

    /* 主系统登陆转接-移动 /mobile/mainSys/logIn ID_201013153908 */
    @RequestMapping(value = "/mobile/mainSys/logIn", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public String mainSysLogIn(HttpServletRequest req, HttpServletResponse response) {
        try {
            /* 处理加密的b64参数 */
            String replace = req.getParameter("value").replace(" ", "+");
            String value = new String(Base64.decodeBase64(replace), "UTF-8");
            JSONObject request = JSONObject.parseObject(value);

            /* 设定检索参数 */
            String user_id = request.getString("user_id");
            String pwd = request.getString("pwd");
            String sys_mark = request.getString("sys_mark");

            /* 调用主登录接口 */
            String urlBack = sysTempController.getinfo(req);
            JSONObject urlBackJson = JSONObject.parseObject(urlBack);

            /* 整理参数进行权限请求 */
            List<WaibaoUserVo> org = null;
            if (urlBackJson.get("org_id") != null && !BlankUtils.checkBlank(urlBackJson.get("org_id").toString())) {
                Map<String, Object> orgVal = new HashMap<>();
                orgVal.put("org_id", urlBackJson.get("org_id"));
                org = wbMapper.selectOrg(orgVal);
            }

            /* 根据权限处理创新菜单 */
            List<WaibaoUserVo> orgMenu = null;
            if (org != null && org.size() > 0 && org.get(0).getPage_list() != null) {
                JSONObject orgNow = JSONObject.parseObject(JSONObject.toJSONString(org.get(0)));
                /* 根据权限获取指定菜单 */
                Map<String, Object> orgMenuMap = new HashMap<String, Object>();
                orgMenuMap.put("page", orgNow.get("page_list").toString().split(","));
                String[] pages = orgNow.get("page_list").toString().split(",");
                orgMenu = wbMapper.selectOrgMenu(pages);
            }

            /* 确定反馈信息 */
            String msg = "success";
            int ret = 1;
            if (orgMenu == null || orgMenu.size() == 0) {
                return ReturnJson.toErrorJson("此账户权限尚未确对应权限功能");
            }
            if (org == null || org.size() == 0 || org.get(0).getPage_list() == null) {
                return ReturnJson.toErrorJson("尚未对此系统进行授权");
            }
            if (urlBackJson.get("org_id") == null) {
                return ReturnJson.toErrorJson("请确认账号密码填写正确");
            }

            /* 生成用户所需数据 */
            CurrUserVo user = new CurrUserVo();
            user.setCompany(urlBackJson.get("company").toString());
            user.setOrg_id(urlBackJson.get("org_id").toString());
            user.setPassword(pwd);
            user.setSys(sys_mark);
            user.setLogin_name(user_id);
            user.setUser_name(user_id);
            user.setOff("0");
            user.setLevel("0");

            /* 生成token */
            String randomStr = BlankUtils.getRandomStr(24);
            redisTemplate.opsForValue()// 随机字符串作为token令牌校验
                    .set("mobiletoken" + randomStr, user, 20 * 60, TimeUnit.SECONDS);

            /* 格式化输出 */
            JSONObject out = new JSONObject();
            out.put("ret", ret);
            out.put("company", urlBackJson.get("company"));
            out.put("msg", msg);
            out.put("org_id", urlBackJson.get("org_id"));
            out.put("org", org != null && org.size() > 0 ? org.get(0) : "");
            out.put("role", "[]");
            out.put("menu", orgMenu);
            out.put("token", "mobiletoken" + randomStr);
            out.put("mainToken", urlBackJson.get("token"));
            out.put("user", user);
            return out.toJSONString();
        } catch (Exception e) {
            e.printStackTrace();
            JSONObject result = new JSONObject();
            result.put("ret", 0);
            result.put("msg", "错误信息 : " + e.getMessage());
            return result.toJSONString();
        }
    }

    /* 主系统修改密码转接-移动 /mobile/mainSys/changePassword ID_201015100417 */
    @RequestMapping(value = "/mobile/mainSys/changePassword", method = {RequestMethod.GET, RequestMethod.POST})
    @ResponseBody
    public String mainSysChangePassword(HttpServletRequest req, HttpServletResponse response) {
        try {
            /* 处理加密的b64参数 */
            String replace = req.getParameter("value").replace(" ", "+");
            String value = new String(Base64.decodeBase64(replace), "UTF-8");
            JSONObject request = JSONObject.parseObject(value);

            /* 进行token验证 */
            if (BlankUtils.checkBlank(request.getString("token"))) {
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            }
            if (redisTemplate.opsForValue().get(request.getString("token")) == null) {
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            } else {
                redisTemplate.opsForValue().set(request.getString("token"), redisTemplate.opsForValue().get(request.getString("token")), 20 * 60,
                        TimeUnit.SECONDS);
            }

            /* 进行主系统登陆 */
            String urlBack = sysTempController.getinfo(req);
            JSONObject urlBackJson = JSONObject.parseObject(urlBack);

            /* 设定修改参数 */
            JSONObject valueObj = new JSONObject();
            valueObj.put("old_pwd", request.getString("old_pwd"));
            valueObj.put("new_pwd", request.getString("new_pwd"));
            valueObj.put("token", urlBackJson.get("token"));//只能用主域的token进行验证

            /* 进行转接处理 */
            req.setAttribute("value", Base64.encodeBase64String(valueObj.toJSONString().getBytes()));
            String urlPwBack = wbSysController.changePassword(null, req);
            JSONObject urlPwBackJson = JSONObject.parseObject(urlPwBack);

            /* 格式化输出 */
            JSONObject out = new JSONObject();
            out.put("ret", urlPwBackJson.get("ret"));
            out.put("msg", urlPwBackJson.get("msg"));
            return out.toJSONString();
        } catch (Exception e) {
            e.printStackTrace();
            JSONObject result = new JSONObject();
            result.put("ret", 0);
            result.put("msg", "错误信息 : " + e.getMessage());
            return result.toJSONString();
        }
    }

    /**
     * 移动系统登录接口 /mobile/login ID_200418172226
     *
     * @param req
     * @return
     */
    @RequestMapping(value = "/mobile/login", method = {RequestMethod.GET, RequestMethod.POST})
    public @ResponseBody
    String loneLgin(HttpServletRequest req) {
        try {
            /* 处理加密的b64参数 */
            String replace = req.getParameter("value").replace(" ", "+");
            String value = new String(Base64.decodeBase64(replace), "UTF-8");
            JSONObject obj = JSONObject.parseObject(value);
            if (BlankUtils.checkBlank(obj.getString("user_id")) || BlankUtils.checkBlank(obj.getString("pw"))) {
                return "{\"ret\":0,\"msg\":\"request value is error!\"}";
            }
            ;
            /* 设定检索参数 */
            Map<String, Object> param = new HashMap<String, Object>();
            param.put("user_id", obj.getString("user_id"));
            param.put("password", obj.getString("pw"));
            /* 库检索用户数据检索 */
            WaibaoUserVo waibaoUserVo = wbMapper.selectUser(param);
            if (waibaoUserVo == null) {

                JSONObject result = new JSONObject();
                result.put("ret", 0);
                result.put("msg", "账号或者密码不正确");
                return result.toJSONString();
            }

            /* 转换实体 **/
            CurrUserVo user = new CurrUserVo();
            user.setLogin_name(waibaoUserVo.getUser_id());
            user.setPassword(waibaoUserVo.getPassword());
            user.setUser_name(waibaoUserVo.getUser_name());
            user.setNick_name(waibaoUserVo.getUser_name());
            user.setCompany(waibaoUserVo.getCompany());
            user.setOrg_id(waibaoUserVo.getOrg_id());
            user.setOrg_name(waibaoUserVo.getOrg_name());
            user.setSys(waibaoUserVo.getSys());

            /* 根据返回的用户数据,查询对应权限数据 */
            List<WaibaoUserVo> org = null;
            if (!BlankUtils.checkBlank(user.getOrg_id())) {
                Map<String, Object> orgMap = new HashMap<String, Object>();
                orgMap.put("org_id", user.getOrg_id());
                org = wbMapper.selectOrg(orgMap);
            }
            ;
            /* 根据权限获取指定菜单 */
            List<WaibaoUserVo> orgMenu = null;
            if (org != null && org.size() > 0) {
                Map<String, Object> orgMenuMap = new HashMap<String, Object>();
                orgMenuMap.put("page", org.get(0).getPage_list().split(","));
                String[] pages = org.get(0).getPage_list().split(",");
                orgMenu = wbMapper.selectOrgMenu(pages);
            }
            ;
            /* 生成token */
            String randomStr = BlankUtils.getRandomStr(24);
            redisTemplate.opsForValue()// 随机字符串作为token令牌校验
                    .set("mobiletoken" + randomStr, user, 20 * 60, TimeUnit.SECONDS);
            /* 格式化输出 */
            JSONObject out = new JSONObject();
            out.put("ret", 1);
            out.put("company", 1);
            out.put("msg", "success");
            out.put("org_id", "roo_t_exp");
            out.put("user", user);
            out.put("org", org != null && org.size() > 0 ? org.get(0) : "");
            out.put("role", "[]");
            out.put("menu", orgMenu);
            out.put("token", "mobiletoken" + randomStr);
            return out.toJSONString();
        } catch (Exception e) {
            e.printStackTrace();
            JSONObject result = new JSONObject();
            result.put("ret", 0);
            result.put("msg", "错误信息 : " + e.getMessage());
            return result.toJSONString();
        }
    }

    /**
     * 应用列表_查询 /mobile/selectApp ID_200521101105 http://localhost:6105/mobile/selectApp?app_key&app_name
     *
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/mobile/selectApp", method = {RequestMethod.GET, RequestMethod.POST})
    public @ResponseBody
    String selectApp(WaibaoUserVo cu, HttpServletRequest request, HttpServletResponse response) {
        try {
            /* 进行token验证 */
            if (BlankUtils.checkBlank(cu.getToken())) {
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            }
            ;
            if (redisTemplate.opsForValue().get(cu.getToken()) == null) {
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            } else {
                redisTemplate.opsForValue().set(cu.getToken(), redisTemplate.opsForValue().get(cu.getToken()), 20 * 60,
                        TimeUnit.SECONDS);
            }
            ;
            /* 设定翻页 */
            // String start = BlankUtils.checkNull(request, "start");
            // String limit = BlankUtils.checkNull(request, "limit");
            // int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
            // int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
            // int pageNo = (pageStart / pageSize) + 1;
            // PageHelper.startPage(pageNo, pageSize); // 进行分页
            /* 设定检索参数 */
            Map<String, Object> selectMap = new HashMap<String, Object>();
            selectMap.put("app_key", request.getParameter("app_key"));
            selectMap.put("app_name", request.getParameter("app_name"));
            /* 库检索菜单数据 */
            List<WaibaoUserVo> dataList = wbMapper.selectApp(selectMap);
            /* 获取总条数 */
            // long total = ((Page) dataList).getTotal();
            /* 格式化输出 */
            JSONObject out = new JSONObject();
            out.put("ret", 1);
            out.put("msg", "success");
            out.put("data", dataList);
            // out.put("totalCount", total);
            return out.toJSONString();
        } catch (Exception e) {
            e.printStackTrace();
            JSONObject result = new JSONObject();
            result.put("ret", 0);
            result.put("msg", "错误信息 : " + e.getMessage());
            return result.toJSONString();
        }
    }

    /**
     * 应用列表_操作 /mobile/conApp ID_200521183023 http://localhost:6105/mobile/conApp?handle&app_key&app_name
     *
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/mobile/conApp", method = {RequestMethod.GET, RequestMethod.POST})
    public @ResponseBody
    String conApp(WaibaoUserVo cu, HttpServletRequest request, HttpServletResponse response) {
        try {
            /* 进行token验证 */
            if (BlankUtils.checkBlank(cu.getToken())) {
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            }
            ;
            if (redisTemplate.opsForValue().get(cu.getToken()) == null) {
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            } else {
                redisTemplate.opsForValue().set(cu.getToken(), redisTemplate.opsForValue().get(cu.getToken()), 20 * 60,
                        TimeUnit.SECONDS);
            }
            ;
            /* 设定检索参数 */
            Map<String, Object> urlValMap = new HashMap<String, Object>();
            urlValMap.put("role_id", request.getParameter("role_id"));
            urlValMap.put("role_name", request.getParameter("role_name"));
            urlValMap.put("role_app_slot", request.getParameter("role_app_slot"));
            /* 根据操作标识符确定操作方式 */
            int res = 0;
            if (request.getParameter("handle") == null) {
                return "{\"ret\":0,\"msg\":\"handle is null!\"}";
            } else if ("add".equals(request.getParameter("handle").toString())) {
                res = wbMapper.insertWbRole(urlValMap);
            } else if ("edit".equals(request.getParameter("handle").toString())) {
                res = wbMapper.updateWbRole(urlValMap);
            } else if ("del".equals(request.getParameter("handle").toString())) {
                res = wbMapper.deleteWbRole(urlValMap);
            } else {
                return "{\"ret\":0,\"msg\":\"handle is error!\"}";
            }
            /* 格式化输出 */
            JSONObject out = new JSONObject();
            out.put("ret", res);
            out.put("msg", "success");
            return out.toJSONString();
        } catch (Exception e) {
            e.printStackTrace();
            JSONObject result = new JSONObject();
            /* 检测参数 */
            if (request.getParameter("role_id") == null || request.getParameter("role_id") == "") {
                result.put("ret", 0);
                result.put("msg", "错误信息 : 必须使用角色id!");
            } else if (e.toString().indexOf("Duplicate entry") > -1) {
                result.put("ret", 0);
                result.put("msg", "错误信息 : 标识已存在!");
            } else {
                result.put("ret", 0);
                result.put("msg", "错误信息 : " + e.getMessage());
            }
            ;
            return result.toJSONString();
        }
    }


    /**
     * 角色列表_查询 /mobile/selectRole ID_200519193239 http://localhost:6105/mobile/selectRole?role_id&role_name
     *
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/mobile/selectRole", method = {RequestMethod.GET, RequestMethod.POST})
    public @ResponseBody
    String selectRole(WaibaoUserVo cu, HttpServletRequest request, HttpServletResponse response) {
        try {
            /* 进行token验证 */
            if (BlankUtils.checkBlank(cu.getToken())) {
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            }
            ;
            if (redisTemplate.opsForValue().get(cu.getToken()) == null) {
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            } else {
                redisTemplate.opsForValue().set(cu.getToken(), redisTemplate.opsForValue().get(cu.getToken()), 20 * 60,
                        TimeUnit.SECONDS);
            }
            ;
            /* 设定翻页 */
            // String start = BlankUtils.checkNull(request, "start");
            // String limit = BlankUtils.checkNull(request, "limit");
            // int pageStart = "".equals(start) == true ? 0 : Integer.parseInt(start);
            // int pageSize = "".equals(limit) == true ? 100 : Integer.parseInt(limit);
            // int pageNo = (pageStart / pageSize) + 1;
            // PageHelper.startPage(pageNo, pageSize); // 进行分页
            /* 设定检索参数 */
            Map<String, Object> roleMap = new HashMap<String, Object>();
            roleMap.put("role_id", request.getParameter("role_id"));
            roleMap.put("role_name", request.getParameter("role_name"));
            /* 库检索菜单数据 */
            List<WaibaoUserVo> role = wbMapper.selectRole(roleMap);
            /* 获取总条数 */
            // long total = ((Page) role).getTotal();
            /* 格式化输出 */
            JSONObject out = new JSONObject();
            out.put("ret", 1);
            out.put("msg", "success");
            out.put("data", role);
            // out.put("totalCount", total);
            return out.toJSONString();
        } catch (Exception e) {
            e.printStackTrace();
            JSONObject result = new JSONObject();
            result.put("ret", 0);
            result.put("msg", "错误信息 : " + e.getMessage());
            return result.toJSONString();
        }
    }


    /**
     * 角色列表_操作 /mobile/conRole ID_200521101019 http://localhost:6105/mobile/conRole?handle&role_id&role_name&role_app_slot
     *
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/mobile/conRole", method = {RequestMethod.GET, RequestMethod.POST})
    public @ResponseBody
    String conRole(WaibaoUserVo cu, HttpServletRequest request, HttpServletResponse response) {
        try {
            /* 进行token验证 */
            if (BlankUtils.checkBlank(cu.getToken())) {
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            }
            ;
            if (redisTemplate.opsForValue().get(cu.getToken()) == null) {
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            } else {
                redisTemplate.opsForValue().set(cu.getToken(), redisTemplate.opsForValue().get(cu.getToken()), 20 * 60,
                        TimeUnit.SECONDS);
            }
            ;
            /* 设定检索参数 */
            Map<String, Object> urlValMap = new HashMap<String, Object>();
            urlValMap.put("role_id", request.getParameter("role_id"));
            urlValMap.put("role_name", request.getParameter("role_name"));
            urlValMap.put("role_app_slot", request.getParameter("role_app_slot"));
            /* 根据操作标识符确定操作方式 */
            int res = 0;
            if (request.getParameter("handle") == null) {
                return "{\"ret\":0,\"msg\":\"handle is null!\"}";
            } else if ("add".equals(request.getParameter("handle").toString())) {
                res = wbMapper.insertWbRole(urlValMap);
            } else if ("edit".equals(request.getParameter("handle").toString())) {
                res = wbMapper.updateWbRole(urlValMap);
            } else if ("del".equals(request.getParameter("handle").toString())) {
                res = wbMapper.deleteWbRole(urlValMap);
            } else {
                return "{\"ret\":0,\"msg\":\"handle is error!\"}";
            }
            /* 格式化输出 */
            JSONObject out = new JSONObject();
            out.put("ret", res);
            out.put("msg", "success");
            return out.toJSONString();
        } catch (Exception e) {
            e.printStackTrace();
            JSONObject result = new JSONObject();
            /* 检测参数 */
            if (request.getParameter("role_id") == null || request.getParameter("role_id") == "") {
                result.put("ret", 0);
                result.put("msg", "错误信息 : 必须使用角色id!");
            } else if (e.toString().indexOf("Duplicate entry") > -1) {
                result.put("ret", 0);
                result.put("msg", "错误信息 : 标识已存在!");
            } else {
                result.put("ret", 0);
                result.put("msg", "错误信息 : " + e.getMessage());
            }
            ;
            return result.toJSONString();
        }
    }


    /**
     * 菜单设置_查询 /mobile/menu ID_200423122058 http://localhost:6105/mobile/menu?style=page&title&index
     *
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/mobile/menu", method = {RequestMethod.GET, RequestMethod.POST})
    public @ResponseBody
    String loneMenu(WaibaoUserVo cu, HttpServletRequest request, HttpServletResponse response) {
        try {
            /* 进行token验证 */
            if (BlankUtils.checkBlank(cu.getToken())) {
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            }
            ;
            if (redisTemplate.opsForValue().get(cu.getToken()) == null) {
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            } else {
                redisTemplate.opsForValue().set(cu.getToken(), redisTemplate.opsForValue().get(cu.getToken()), 20 * 60,
                        TimeUnit.SECONDS);
            }
            /* 设定检索参数 */
            Map<String, Object> menuMap = new HashMap<String, Object>();
            menuMap.put("style", request.getParameter("style"));
            menuMap.put("index", request.getParameter("index"));
            menuMap.put("title", request.getParameter("title"));
            /* 库检索菜单数据 */
            List<WaibaoUserVo> menu = wbMapper.selectMenu(menuMap);
            /* 格式化输出 */
            JSONObject out = new JSONObject();
            out.put("ret", 1);
            out.put("msg", "success");
            out.put("data", menu);
            return out.toJSONString();
        } catch (Exception e) {
            e.printStackTrace();
            JSONObject result = new JSONObject();
            result.put("ret", 0);
            result.put("msg", "错误信息 : " + e.getMessage());
            return result.toJSONString();
        }
    }


    /**
     * 菜单设置_操作 /mobile/menuSet ID_200423191535 http://localhost:6105/mobile/menuSet?handle=add.edit.del&index&title&style&menu&off&icon&slot
     *
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/mobile/menuSet", method = {RequestMethod.GET, RequestMethod.POST})
    public @ResponseBody
    String loneMenuSet(WaibaoUserVo cu, HttpServletRequest request, HttpServletResponse response) {
        try {
            /* 进行token验证 */
            if (BlankUtils.checkBlank(cu.getToken())) {
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            }
            ;
            if (redisTemplate.opsForValue().get(cu.getToken()) == null) {
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            } else {
                redisTemplate.opsForValue().set(cu.getToken(), redisTemplate.opsForValue().get(cu.getToken()), 20 * 60,
                        TimeUnit.SECONDS);
            }
            ;
            /* 设定检索参数 */
            Map<String, Object> menuMap = new HashMap<String, Object>();
            menuMap.put("index", request.getParameter("index"));
            menuMap.put("title", request.getParameter("title"));
            menuMap.put("style", request.getParameter("style"));
            menuMap.put("menu", request.getParameter("menu"));
            menuMap.put("off", request.getParameter("off"));
            menuMap.put("icon", request.getParameter("icon"));
            menuMap.put("slot", request.getParameter("slot"));
            /* 根据操作标识符确定操作方式 */
            int res = 0;
            if (request.getParameter("handle") == null) {
                return "{\"ret\":0,\"msg\":\"handle is null!\"}";
            } else if ("add".equals(request.getParameter("handle").toString())) {
                res = wbMapper.insertWbMenu(menuMap);
            } else if ("edit".equals(request.getParameter("handle").toString())) {
                res = wbMapper.updateWbMenu(menuMap);
            } else if ("del".equals(request.getParameter("handle").toString())) {
                res = wbMapper.deleteWbMenu(menuMap);
            } else {
                return "{\"ret\":0,\"msg\":\"handle is error!\"}";
            }
            /* 格式化输出 */
            JSONObject out = new JSONObject();
            out.put("ret", res);
            out.put("msg", "success");
            return out.toJSONString();
        } catch (Exception e) {
            e.printStackTrace();
            JSONObject result = new JSONObject();
            if (e.toString().indexOf("Duplicate entry") > -1) {
                result.put("ret", 0);
                result.put("msg", "错误信息 : 标识已存在!");
            } else {
                result.put("ret", 0);
                result.put("msg", "错误信息 : " + e.getMessage());
            }
            ;
            return result.toJSONString();
        }
    }


    /**
     * 权限接口_查询 /mobile/org ID_200424161225 http://localhost:6105/mobile/org?org_id&org_name&hidden_menu_list&page_list
     *
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/mobile/org", method = {RequestMethod.GET, RequestMethod.POST})
    public @ResponseBody
    String loneOrg(WaibaoUserVo cu, HttpServletRequest request, HttpServletResponse response) {
        try {
            /* 进行token验证 */
            if (BlankUtils.checkBlank(cu.getToken())) {
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            }
            ;
            if (redisTemplate.opsForValue().get(cu.getToken()) == null) {
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            } else {
                redisTemplate.opsForValue().set(cu.getToken(), redisTemplate.opsForValue().get(cu.getToken()), 20 * 60,
                        TimeUnit.SECONDS);
            }
            /* 设定检索参数 */
            Map<String, Object> orgMap = new HashMap<String, Object>();
            orgMap.put("org_id", request.getParameter("org_id"));
            orgMap.put("org_name", request.getParameter("org_name"));
            orgMap.put("hidden_menu_list", request.getParameter("hidden_menu_list"));
            orgMap.put("page_list", request.getParameter("page_list"));
            /* 库检索菜单数据 */
            List<WaibaoUserVo> org = wbMapper.selectOrg(orgMap);
            /* 格式化输出 */
            JSONObject out = new JSONObject();
            out.put("ret", 1);
            out.put("msg", "success");
            out.put("data", org);
            return out.toJSONString();
        } catch (Exception e) {
            e.printStackTrace();
            JSONObject result = new JSONObject();
            result.put("ret", 0);
            result.put("msg", "错误信息 : " + e.getMessage());
            return result.toJSONString();
        }
    }


    /**
     * 权限接口_操作 /mobile/orgSet ID_200424164102 http://localhost:6105/mobile/orgSet?org_id&org_name&hidden_menu_list&page_list
     *
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/mobile/orgSet", method = {RequestMethod.GET, RequestMethod.POST})
    public @ResponseBody
    String loneOrgSet(WaibaoUserVo cu, HttpServletRequest request, HttpServletResponse response) {
        try {
            /* 进行token验证 */
            if (BlankUtils.checkBlank(cu.getToken())) {
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            }
            ;
            if (redisTemplate.opsForValue().get(cu.getToken()) == null) {
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            } else {
                redisTemplate.opsForValue().set(cu.getToken(), redisTemplate.opsForValue().get(cu.getToken()), 20 * 60,
                        TimeUnit.SECONDS);
            }
            ;
            /* 设定检索参数 */
            Map<String, Object> orgMap = new HashMap<String, Object>();
            orgMap.put("org_id", request.getParameter("org_id"));
            orgMap.put("org_name", request.getParameter("org_name"));
            orgMap.put("hidden_menu_list", request.getParameter("hidden_menu_list"));
            orgMap.put("page_list", request.getParameter("page_list"));
            orgMap.put("sys_type",request.getParameter("sys_type"));
            /* 根据标识做对应操作 */
            int res = 0;
            if (request.getParameter("handle") == null) {
                return "{\"ret\":0,\"msg\":\"handle is null!\"}";
            } else if ("add".equals(request.getParameter("handle").toString())) {
                res = wbMapper.insertWbOrg(orgMap);
            } else if ("edit".equals(request.getParameter("handle").toString())) {
                res = wbMapper.upDataWbOrg(orgMap);
            } else if ("del".equals(request.getParameter("handle").toString())) {
                res = wbMapper.deleteWbOrg(orgMap);
            } else {
                return "{\"ret\":0,\"msg\":\"handle is error!\"}";
            }
            /* 格式化输出 */
            JSONObject out = new JSONObject();
            out.put("ret", res);
            out.put("msg", "success");
            return out.toJSONString();
        } catch (Exception e) {
            e.printStackTrace();
            JSONObject result = new JSONObject();
            result.put("ret", 0);
            if (e.toString().indexOf("Duplicate entry") > -1) {
                result.put("msg", "错误信息 : 标识已存在!");
            } else if (e.toString().indexOf("doesn't have a default value") > -1) {
                result.put("msg", "错误信息 : 部分字段缺少默认值!");
            } else {
                result.put("msg", "错误信息 : " + e.getMessage());
            }
            ;
            return result.toJSONString();
        }
    }


    /**
     * 用户管理_查询 /mobile/userList ID_200427142009 http://localhost:6105/mobile/userList?user_id&password&user_name&org_id&sys
     *
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/mobile/userList", method = {RequestMethod.GET, RequestMethod.POST})
    public @ResponseBody
    String loneUserList(WaibaoUserVo cu, HttpServletRequest request, HttpServletResponse response) {
        try {
            /* 进行token验证 */
            if (BlankUtils.checkBlank(cu.getToken())) {
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            }
            ;
            if (redisTemplate.opsForValue().get(cu.getToken()) == null) {
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            } else {
                redisTemplate.opsForValue().set(cu.getToken(), redisTemplate.opsForValue().get(cu.getToken()), 20 * 60,
                        TimeUnit.SECONDS);
            }
            /* 设定检索参数 */
            Map<String, Object> userListMap = new HashMap<String, Object>();
            userListMap.put("user_id", request.getParameter("user_id"));
            userListMap.put("password", request.getParameter("password"));
            userListMap.put("user_name", request.getParameter("user_name"));
            userListMap.put("org_id", request.getParameter("org_id"));
            userListMap.put("role_id", request.getParameter("role_id"));
            userListMap.put("sys", request.getParameter("sys"));
            /* 库检索菜单数据 */
            List<WaibaoUserVo> userList = wbMapper.selectUserList(userListMap);
            /* 格式化输出 */
            JSONObject out = new JSONObject();
            out.put("ret", 1);
            out.put("msg", "success");
            out.put("data", userList);
            return out.toJSONString();
        } catch (Exception e) {
            e.printStackTrace();
            JSONObject result = new JSONObject();
            result.put("ret", 0);
            result.put("msg", "错误信息 : " + e.getMessage());
            return result.toJSONString();
        }
    }


    /**
     * 用户管理_操作 /mobile/userListSet ID_200427144719 http://localhost:6105/mobile/userListSet?user_id&password&user_name&org_id&sys
     *
     * @param cu
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/mobile/userListSet", method = {RequestMethod.GET, RequestMethod.POST})
    public @ResponseBody
    String loneUserListSet(WaibaoUserVo cu, HttpServletRequest request, HttpServletResponse response) {
        try {
            /* 进行token验证 */
            if (BlankUtils.checkBlank(cu.getToken())) {
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            }
            ;
            if (redisTemplate.opsForValue().get(cu.getToken()) == null) {
                return "{\"ret\":2,\"msg\":\"token is error!\"}";
            } else {
                redisTemplate.opsForValue().set(cu.getToken(), redisTemplate.opsForValue().get(cu.getToken()), 20 * 60,
                        TimeUnit.SECONDS);
            }
            ;
            /* 设定检索参数 */
            Map<String, Object> userListSet = new HashMap<String, Object>();
            userListSet.put("user_id", request.getParameter("user_id"));
            userListSet.put("password", request.getParameter("password"));
            userListSet.put("user_name", request.getParameter("user_name"));
            userListSet.put("org_id", request.getParameter("org_id"));
            userListSet.put("role_id", request.getParameter("role_id"));
            userListSet.put("company",request.getParameter("company"));
            if (request.getParameter("sys") != null) {
                userListSet.put("sys", request.getParameter("sys"));
            } else {
                userListSet.put("sys", "oneSys");
            }
            ;
            /* 根据标识做对应操作 */
            int res = 0;
            String msg = "";
            if (request.getParameter("handle") == null) {
                return "{\"ret\":0,\"msg\":\"handle is null!\"}";
            } else if ("add".equals(request.getParameter("handle").toString())) {
                res = wbMapper.insertUserList(userListSet);
            } else if ("edit".equals(request.getParameter("handle").toString())) {
                res = wbMapper.upDataUserList(userListSet);
            } else if ("del".equals(request.getParameter("handle").toString())) {
                res = wbMapper.deleteUserList(userListSet);
            } else {
                return "{\"ret\":0,\"msg\":\"handle is error!\"}";
            }
            /* 格式化输出 */
            JSONObject out = new JSONObject();
            out.put("ret", res);
            out.put("msg", "success");
            return out.toJSONString();
        } catch (Exception e) {
            e.printStackTrace();
            JSONObject result = new JSONObject();
            result.put("ret", 0);
            if (e.toString().indexOf("Duplicate entry") > -1) {
                result.put("msg", "错误信息 : 标识已存在!");
            } else if (e.toString().indexOf("doesn't have a default value") > -1) {
                result.put("msg", "错误信息 : 部分字段缺少默认值!");
            } else {
                result.put("msg", "错误信息 : " + e.getMessage());
            }
            ;
            return result.toJSONString();
        }
    }


    /**
     * 用户修改密码 ID_200628113015
     *
     * @param cu
     * @param req
     * @return
     */
    @RequestMapping(value = "/mobile/changePassword", method = {RequestMethod.GET, RequestMethod.POST})
    public @ResponseBody
    String changePassword(WaibaoUserVo cu, HttpServletRequest req) {
        try {
            String replace = req.getParameter("value").replace(" ", "+");
            String value = new String(Base64.decodeBase64(replace), "UTF-8");
            JSONObject obj = JSONObject.parseObject(value);
            if (BlankUtils.checkBlank(obj.getString("token")) || BlankUtils.checkBlank(obj.getString("old_pwd"))
                    || BlankUtils.checkBlank(obj.getString("new_pwd"))) {
                return "{\"ret\":0,\"msg\":\"request value is error!\"}";
            }

            CurrUserVo cuser = (CurrUserVo) redisTemplate.opsForValue().get(obj.getString("token"));
            // System.out.println(JSON.toJSONString(cuser));
            if (cuser == null || !obj.getString("old_pwd").equals(cuser.getPassword())) {
                return "{\"ret\":2,\"msg\":\"token or password is error!\"}";
            } else {
                cuser.setPassword(obj.getString("new_pwd"));
                redisTemplate.opsForValue().set(obj.getString("token"), cuser, 20 * 60, TimeUnit.SECONDS);
            }

            /* 转换实体 **/
            WaibaoUserVo user = new WaibaoUserVo();
            user.setPassword(cuser.getPassword());
            user.setUser_id(cuser.getLogin_name());

            int res = wbMapper.updateWbSysUserForPassword(user);
            JSONObject result = new JSONObject();
            if (res > 0) {
                result.put("ret", 1);
                result.put("msg", "success");
            } else {
                result.put("ret", 0);
                result.put("msg", "fail");
            }

            return result.toJSONString();
        } catch (Exception e) {
            e.printStackTrace();
            JSONObject result = new JSONObject();
            result.put("ret", 0);
            result.put("msg", "错误信息 : " + e.getMessage());
            return result.toJSONString();
        }
    }

}
