package com.wbgame.service;

import org.junit.Test;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR> Assistant
 * @Description 邮件验证码服务测试类
 * @Date 2025/01/16
 */
public class EmailVerificationCodeServiceTest {

    // 华为验证码正则：验证码： 173646
    private static final Pattern HUAWEI_PATTERN = Pattern.compile("验证码[：:]\\s*(\\d{6})");
    
    // 小米验证码正则：验证码是：303729
    private static final Pattern XIAOMI_PATTERN = Pattern.compile("验证码是[：:]\\s*(\\d{6})");

    public static void main(String[] args) {
        new EmailVerificationCodeServiceTest().testHuaweiCodeParsing();
    }

    public void testHuaweiCodeParsing() {
//        String huaweiContent = "华为账号邮件验证码\n[图片] 您好！ 验证码： 173646 ，用于登录华为账号 171******08 。转给他人将导致华为账号被盗和个人信息泄露，谨防诈骗。如非您操作请忽略。 此致 华为 用户协议 | 隐私声明 | 常见问题 | Cookies 华为账号 版权所有 © 2011-2025";
        String huaweiContent = "您好！ 验证码：631667，用于荣耀账号 177******23 绑定本邮件地址。 此致 荣耀 荣耀账号用户协议  |  关于荣耀账号与隐私的声明  |  常见问题  |  Cookies 版权所有 © 2021-2025 荣耀。保留一切权利。";

        Matcher matcher = HUAWEI_PATTERN.matcher(huaweiContent);
        if (matcher.find()) {
            String code = matcher.group(1);
            System.out.println("华为验证码解析成功：" + code);
            assert "173646".equals(code);
        } else {
            System.out.println("华为验证码解析失败");
            assert false;
        }
    }

    @Test
    public void testXiaomiCodeParsing() {
        String xiaomiContent = "小米账号登录验证\n亲爱的小米用户雪道科技，您好！ 您本次登录小米账号15*****212的验证码是：303729 请勿将验证码透露给其他人。 如非本人操作，请立即访问id.mi.com修改密码。 本邮件由系统自动发送，请勿直接回复！ 感谢您的访问，祝您使用愉快\n发件人：\nXiaomi Corporation <­<EMAIL>­>";
        
        Matcher matcher = XIAOMI_PATTERN.matcher(xiaomiContent);
        if (matcher.find()) {
            String code = matcher.group(1);
            System.out.println("小米验证码解析成功：" + code);
            assert "303729".equals(code);
        } else {
            System.out.println("小米验证码解析失败");
            assert false;
        }
    }

    @Test
    public void testBothPatterns() {
        System.out.println("=== 邮件验证码解析测试 ===");
        testHuaweiCodeParsing();
        testXiaomiCodeParsing();
        System.out.println("=== 所有测试通过 ===");
    }
}
