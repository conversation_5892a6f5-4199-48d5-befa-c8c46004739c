<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wbgame.mapper.master.EmailVerificationCodeMapper">

    <!-- 批量插入邮件验证码记录 -->
    <insert id="batchInsert" parameterType="java.util.List">
        replace into dn_email_verification_code (
            email, message_id, subject, content, verification_code, platform, create_time, update_time
        ) values
        <foreach collection="codeList" item="item" separator=",">
            (#{item.email}, #{item.messageId}, #{item.subject}, #{item.content}, 
             #{item.verificationCode}, #{item.platform}, now(), now())
        </foreach>
    </insert>

    <!-- 根据邮箱查询验证码记录 -->
    <select id="queryByEmail" parameterType="com.wbgame.pojo.EmailVerificationCodeDTO" 
            resultType="com.wbgame.pojo.EmailVerificationCodeVo">
        select 
            id,
            email,
            subject,
            verification_code as verificationCode,
            platform,
            DATE_FORMAT(create_time, '%Y-%m-%d %H:%i:%s') as createTime
        from dn_email_verification_code
        where 1=1
        <if test="email != null and email != ''">
            and email = #{email}
        </if>
        <if test="platform != null and platform != ''">
            and platform = #{platform}
        </if>
        <if test="verificationCode != null and verificationCode != ''">
            and verification_code = #{verificationCode}
        </if>
        <if test="startTime != null and startTime != ''">
            and create_time >= #{startTime}
        </if>
        order by create_time desc
    </select>


</mapper>
