package com.wbgame.mapper.master;

import com.wbgame.pojo.EmailVerificationCodeDTO;
import com.wbgame.pojo.EmailVerificationCodeVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> Assistant
 * @Description 邮件验证码数据访问层
 * @Date 2025/01/16
 */
public interface EmailVerificationCodeMapper {

    /**
     * 批量插入邮件验证码记录
     *
     * @param codeList 验证码记录列表
     * @return 插入条数
     */
    int batchInsert(@Param("codeList") List<EmailVerificationCodeVo> codeList);

    /**
     * 根据邮箱查询验证码记录
     *
     * @param dto 查询参数
     * @return 验证码记录列表
     */
    EmailVerificationCodeVo queryByEmail(EmailVerificationCodeDTO dto);


}
