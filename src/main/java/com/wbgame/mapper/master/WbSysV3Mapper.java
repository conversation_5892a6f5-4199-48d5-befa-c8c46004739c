package com.wbgame.mapper.master;

import java.util.List;
import java.util.Map;

import com.wbgame.pojo.*;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

public interface WbSysV3Mapper {
	
	/** 通过子系统标识获取菜单列表 */
	List<CurrMenuVo> selectSysmarkMenu(CurrMenuVo cur);

	/** 菜单配置操作 */
	int insertSysmarkMenu(CurrMenuVo cur);
	int updateSysmarkMenu(CurrMenuVo cur);
	int deleteSysmarkMenu(CurrMenuVo cur);
	
	
	/** 获取权限列表 */
	List<CurrOrgVo> selectSysmarkOrg(CurrOrgVo cur);

	/** 权限配置操作 */
	int insertSysmarkOrg(CurrOrgVo cur);
	int updateSysmarkOrg(CurrOrgVo cur);
	int deleteSysmarkOrg(CurrOrgVo cur);
	
	
	/** 获取角色列表 */
	List<CurrRoleVo> selectSysmarkRole(CurrRoleVo cur);

	/** 角色配置操作 */
	int insertSysmarkRole(CurrRoleVo cur);
	int updateSysmarkRole(CurrRoleVo cur);
	int deleteSysmarkRole(CurrRoleVo cur);
	
	
	/** 获取用户列表 */
	List<CurrUserVo> selectSysmarkUser(CurrUserVo cur);
	
	/** 用户配置操作 */
	int insertSysmarkUser(CurrUserVo cur);
	int updateSysmarkUser(CurrUserVo cur);
	int deleteSysmarkUser(CurrUserVo cur);
	
	/** 获取公司部门列表接口 */
	@Select("SELECT id,department_name FROM main_sys_department_v3 ")
	List<Map<String, Object>> selectSysDepartList();
	
	/** 获取各子系统的列表接口 */
	@Select("SELECT * FROM main_sys_menu_v3 where sys_mark=`index`")
	List<CurrMenuVo> selectSysMarkList();
	
	/** 查询登录用户的基本信息 */
	@Select("SELECT aa.*,bb.* FROM main_sys_user_v3 aa LEFT JOIN main_sys_org_v3 bb ON aa.org_id = bb.org_id WHERE login_name = #{login_name} and password = #{password} and company in ('1','2','3','4') and mach_code='1' ")
	CurrUserVo selectWbSysUser(Map<String, Object> map);
	
	/** 查询飞书登录用户的基本信息 */
	@Select("SELECT aa.*,bb.*,cc.fsuser_id FROM main_sys_user_v3 aa LEFT JOIN main_sys_org_v3 bb ON aa.org_id=bb.org_id LEFT JOIN wb_fs_account_info cc ON aa.login_name=cc.login_name WHERE cc.fsuser_id = #{fsuser_id} and aa.company in ('1','2','3','4')")
	CurrUserVo selectFsSysUser(Map<String, Object> map);
	
	
	/** 记录登录日志 */
	@Insert("insert into main_sys_login_log(login_name,nick_name,org_name,company,client_ip,create_time) values(#{login_name},#{nick_name},#{org_name},#{company},#{client_ip},now())")
	int insertWbSysLoginLog(CurrUserVo cu);
	
	/** 出包工具配置查询和修改  */
	@Select("select * from dnwx_client.wbgui_userinfo where account = #{login_name}")
	WbguiUserInfoVo selectWbguiUserInfo(WbguiUserInfoVo wbu);
	@Update("update dnwx_client.wbgui_userinfo set accountPm=#{accountPm},formPm=#{formPm},iconPm=#{iconPm},gameTypePm=#{gameTypePm},channelPm=#{channelPm},adPm=#{adPm},testPm=#{testPm},createApkPm=#{createApkPm},modulePm=#{modulePm},administratorPm=#{administratorPm},mtPm=#{mtPm},pmCopyright=#{pmCopyright} where account=#{login_name}")
	int updateWbguiUserInfo(WbguiUserInfoVo wbu);
	
	/** 用户名与飞书关联信表查询  */
	@MapKey("mapkey")
	@Select("select login_name mapkey,department,post,fsuser_id from wb_fs_account_info")
	Map<String, Map<String, String>> selectFsAccountInfo();
	
	
	/** 获取前端错误日志列表 */
	List<ClientErrorLogVo> selectClientErrorLog(ClientErrorLogVo client);

	/** 前端错误日志操作 */
	int insertClientErrorLog(ClientErrorLogVo client);
	int updateClientErrorLog(ClientErrorLogVo client);
	int deleteClientErrorLog(ClientErrorLogVo client);
	
	@Update(" ${sql} ")
	public int execSql(@Param("sql")String sql); // 直接执行DML sql语句
	@Select(" ${sql} ")
	public List<String> queryListString(@Param("sql")String sql);
	@Select(" ${sql} ")
	public List<Map<String, Object>> queryListMap(@Param("sql")String sql);
	@Select(" ${sql} ")
	public List<Map<String, String>> queryListMapOne(@Param("sql")String sql);
	@Update(" ${sql} ")
	public int execSqlHandle(@Param("sql")String sql, @Param("obj")Object obj);

	/** 广告美术组配置获取广告部门相关用户信息(从V2获取改到V3获取)*/
	List<CurrUserVo> selectArtOrgUserInfo();


	List<WbMenuConfigVo> selectSysmarkMenuConfig(WbMenuConfigVo cur);

}