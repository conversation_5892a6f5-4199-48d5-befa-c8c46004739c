package com.wbgame.service;

import com.wbgame.pojo.EmailVerificationCodeDTO;
import com.wbgame.pojo.EmailVerificationCodeVo;

import java.util.List;

/**
 * <AUTHOR> Assistant
 * @Description 邮件验证码业务层接口
 * @Date 2025/01/16
 */
public interface EmailVerificationCodeService {

    /**
     * 拉取邮件并解析验证码
     *
     * @param email 验证邮箱
     * @return 处理结果
     */
    String fetchEmailsAndParseCode(String email);

    /**
     * 根据邮箱查询验证码记录
     *
     * @param dto 查询参数
     * @return 验证码记录列表
     */
    List<EmailVerificationCodeVo> queryVerificationCodes(EmailVerificationCodeDTO dto);
}
